/*
 * Qt-based reimplementation of KiCad BOARD_ITEM class
 * 
 * This class represents the base class for all PCB items, providing 
 * comprehensive layer management, geometric transformations, and 
 * integration with Qt's graphics and property systems.
 */

#pragma once

#include "eda_object_data.h"
#include "qt_temporary_implementations.h"
#include <QtCore/QString>
#include <QtCore/QVector>
#include <QtCore/QPointF>
#include <QtCore/QRectF>
#include <QtCore/QSet>
#include <QtCore/QBitArray>
#include <QtCore/QMetaEnum>
#include <QtCore/QHash>
#include <QtCore/QSharedPointer>
#include <QtGui/QTransform>
#include <memory>
#include <functional>
#include <bitset>

// Forward declarations for dependencies not in migration scope
class EDA_BOARD_DATA;
class QtBoardDesignSettings;
class EDA_BOARD_OBJECT_CONTAINER;
class QtPcbGroup;
class EDA_FOOTPRINT_DATA;
class QtShape;
class QtShapeSegment;
class QtShapePolySet;
struct QtStrokeParams;
class QtRenderSettings;

// Forward declarations for VIEW system
namespace QtKigfx {
    class View;
}

// View update and visibility flags
enum class QtViewUpdateFlags : int {
    None = 0x00,
    Appearance = 0x01,
    Color = 0x02,
    Geometry = 0x04,
    Layers = 0x08,
    InitialAdd = 0x10,
    Repaint = 0x20,
    All = 0xef
};

enum class QtViewVisibilityFlags : int {
    Visible = 0x01,
    Hidden = 0x02,
    OverlayHidden = 0x04
};

// Forward declaration - actual definition is in qt_temporary_implementations.h
enum class QtErrorLoc : int;


// Zone layer override structure is defined in eda_via_data.h
// Forward declaration only to avoid conflicts

// QtLayerSet is defined in qt_temporary_implementations.h
class QtLayerSet;

// Forward declarations - actual definitions are in qt_temporary_implementations.h
enum class QtFlashing : int;
enum class QtFlipDirection : int;

/**
 * @brief Qt-based reimplementation of KiCad's BOARD_ITEM class
 * 
 * This class provides the base functionality for all PCB items, including
 * layer management, geometric transformations, and integration with Qt's
 * graphics and property systems.
 */
class EDA_BOARD_OBJECT_DATA : public EDA_OBJECT_DATA
{
public:
    //==========================================================================
    // CONSTRUCTION AND DESTRUCTION
    //==========================================================================
    explicit EDA_BOARD_OBJECT_DATA(EDA_BOARD_OBJECT_CONTAINER* parent = nullptr, 
                        QtKicadType type = QtKicadType::NotUsed,
                        QtPcbLayerId layer = QtPcbLayerId::FCu);
    
    explicit EDA_BOARD_OBJECT_DATA(const EDA_BOARD_OBJECT_DATA& other);
    ~EDA_BOARD_OBJECT_DATA() override;

    //==========================================================================
    // PURE VIRTUAL METHODS - MUST BE IMPLEMENTED BY DERIVED CLASSES
    //==========================================================================
    virtual double similarity(const EDA_BOARD_OBJECT_DATA& other) const = 0;
    virtual bool operator==(const EDA_BOARD_OBJECT_DATA& other) const = 0;
    
    //==========================================================================
    // LAYER MANAGEMENT
    //==========================================================================
    QtPcbLayerId getLayer() const { return m_layer; }
    virtual void setLayer(QtPcbLayerId layer);
    
    virtual QtLayerSet getLayerSet() const;
    virtual void setLayerSet(const QtLayerSet& layers);
    
    QString getLayerName() const;
    QString layerMaskDescription() const;
    
    bool isOnLayer(QtPcbLayerId layer) const;
    bool isOnCopperLayer() const;
    bool isSideSpecific() const;
    
    // Board layer information
    int boardLayerCount() const;
    int boardCopperLayerCount() const;
    QtLayerSet boardLayerSet() const;
    
    //==========================================================================
    // POSITION AND GEOMETRY
    //==========================================================================
    virtual QPointF getCenter() const;
    
    double getX() const { return getPosition().x(); }
    double getY() const { return getPosition().y(); }
    void setX(double x);
    void setY(double y);
    
    //==========================================================================
    // CONNECTION AND PROPERTIES
    //==========================================================================
    virtual bool isConnected() const { return false; }
    virtual bool hasHole() const { return false; }
    virtual bool hasDrilledHole() const { return false; }
    virtual bool isTented(QtPcbLayerId layer) const { return false; }
    
    //==========================================================================
    // KNOCKOUT AND LOCKING
    //==========================================================================
    virtual bool isKnockout() const { return m_isKnockout; }
    virtual void setKnockout(bool knockout);
    
    virtual bool isLocked() const;
    virtual void setLocked(bool locked);
    
    //==========================================================================
    // GROUP MANAGEMENT
    //==========================================================================
    void setParentGroup(QtPcbGroup* group) { m_group = group; }
    QtPcbGroup* getParentGroup() const { return m_group; }
    
    //==========================================================================
    // PARENT AND BOARD ACCESS
    //==========================================================================
    EDA_BOARD_OBJECT_CONTAINER* getParent() const;
    EDA_FOOTPRINT_DATA* getParentFootprint() const;
    virtual const EDA_BOARD_DATA* getBoard() const;
    virtual EDA_BOARD_DATA* getBoard();
    
    QString getParentAsString() const;
    
    //==========================================================================
    // FOOTPRINT RELATIVE POSITIONING
    //==========================================================================
    QPointF getFPRelativePosition() const;
    void setFPRelativePosition(const QPointF& position);
    
    //==========================================================================
    // GEOMETRIC TRANSFORMATIONS
    //==========================================================================
    /**
     * @brief Move the object by a displacement vector
     * @param moveVector Displacement in X and Y coordinates
     */
    virtual void move(const QPointF& moveVector);
    
    /**
     * @brief Rotate the object around a center point
     * @param rotationCenter Center point for rotation
     * @param angleRadians Angle to rotate in radians
     */
    virtual void rotate(const QPointF& rotationCenter, double angleRadians);
    
    /**
     * @brief Flip the object across a center point
     * @param center Center point for flipping
     * @param flipDirection Direction to flip (horizontal/vertical)
     */
    virtual void flip(const QPointF& center, QtFlipDirection flipDirection);
    
    /**
     * @brief Mirror the object across a center point
     * @param center Center point for mirroring
     * @param flipDirection Direction to mirror (horizontal/vertical)
     */
    virtual void mirror(const QPointF& center, QtFlipDirection flipDirection);
    
    virtual void normalize() {}
    virtual void normalizeForCompare() { normalize(); }
    
    //==========================================================================
    // COMMON ITEM METHODS
    //==========================================================================
    virtual QString getClass() const = 0;  // Pure virtual - must be implemented
    virtual QRectF getBoundingRect() const;  // Returns bounding rectangle
    virtual QRectF getBoundingBox() const { return getBoundingRect(); }  // Alias for compatibility
    // UUID is immutable in the base class - override if needed in derived classes
    
    //==========================================================================
    // SHAPE AND GEOMETRY
    //==========================================================================
    /**
     * @brief Get the effective shape for collision detection and routing
     * @param layer Layer to get shape for (defaults to undefined)
     * @param flash Flashing mode for pads (default/always/never)
     * @return Shared pointer to the shape object
     */
    virtual std::shared_ptr<QtShape> getEffectiveShape(QtPcbLayerId layer = QtPcbLayerId::UndefinedLayer,
                                                      QtFlashing flash = QtFlashing::Default) const;
    
    /**
     * @brief Get the effective hole shape for drilling
     * @return Shared pointer to the hole shape segment
     */
    virtual std::shared_ptr<QtShapeSegment> getEffectiveHoleShape() const;
    
    //==========================================================================
    // STROKE PROPERTIES
    //==========================================================================
    virtual bool hasLineStroke() const { return false; }
    virtual QtStrokeParams getStroke() const;
    virtual void setStroke(const QtStrokeParams& stroke);
    
    //==========================================================================
    // HIERARCHY OPERATIONS
    //==========================================================================
    virtual void runOnChildren(const std::function<void(EDA_BOARD_OBJECT_DATA*)>& function) const {}
    virtual void runOnDescendants(const std::function<void(EDA_BOARD_OBJECT_DATA*)>& function, int depth = 0) const {}
    
    //==========================================================================
    // DUPLICATION AND DATA SWAPPING
    //==========================================================================
    virtual EDA_BOARD_OBJECT_DATA* duplicate() const;
    void swapItemData(EDA_BOARD_OBJECT_DATA* other);
    
    //==========================================================================
    // POLYGON TRANSFORMATION
    //==========================================================================
    /**
     * @brief Transform this object's shape to polygons for zone filling and DRC
     * @param buffer Output polygon set
     * @param layer Layer to transform for
     * @param clearance Additional clearance to add
     * @param error Maximum approximation error for curves
     * @param errorLocation Where to place error (inside/outside/center)
     * @param ignoreLineWidth Whether to ignore line width in transformation
     */
    virtual void transformShapeToPolygon(QtShapePolySet& buffer, QtPcbLayerId layer,
                                        double clearance, double error, 
                                        QtErrorLoc errorLocation = QtErrorLoc::Outside,
                                        bool ignoreLineWidth = false) const;
    
    //==========================================================================
    // VISUALIZATION AND VIEW SUPPORT
    //==========================================================================
    // VIEW_ITEM interface support
    virtual bool renderAsBitmap(double worldScale) const { return isShownAsBitmap(); }
    
    //==========================================================================
    // DELETION
    //==========================================================================
    void deleteStructure();
    
    //==========================================================================
    // STYLESHEET SUPPORT FROM DESIGN SETTINGS
    //==========================================================================
    virtual void styleFromSettings(const QtBoardDesignSettings& settings) {}
    
    //==========================================================================
    // FONT METRICS SUPPORT
    //==========================================================================
    // Note: Font metrics access would require QtFontMetrics implementation
    // virtual const QtFontMetrics& getFontMetrics() const;
    
    //==========================================================================
    // COMPARE FLAGS AND POINTER COMPARISON
    //==========================================================================
    enum CompareFlags : int {
        DrcCompare = 0x01
    };
    
    struct PtrComparator {
        bool operator()(const EDA_BOARD_OBJECT_DATA* a, const EDA_BOARD_OBJECT_DATA* b) const;
    };
    
    //==========================================================================
    // STATIC UTILITIES AND CONSTANTS
    //==========================================================================
    static const QPointF& zeroOffset();
    
    // VIEW_ITEM interface helper
    bool isShownAsBitmap() const
    {
        return hasFlag( EDA_OBJECT_DATAFlag::IsShownAsBitmap );
    }
    
    // Layer utility functions
    static bool isValidLayer(QtPcbLayerId layer);
    static bool isCopperLayer(QtPcbLayerId layer);
    static bool isTechLayer(QtPcbLayerId layer);
    static bool isUserLayer(QtPcbLayerId layer);
    static bool isSolderMaskLayer(QtPcbLayerId layer);
    static QString layerName(QtPcbLayerId layer);
    static QtLayerSet copperLayerSet();
    static QtLayerSet techLayerSet();
    
    // Static zero offset point for drawing operations (matches BOARD_ITEM::ZeroOffset)
    static const QPointF ZeroOffset;
    static const QPointF s_zeroOffset;  // Alias for compatibility
    
protected:
    //==========================================================================
    // PROTECTED HELPER METHODS
    //==========================================================================
    virtual void swapData(EDA_BOARD_OBJECT_DATA* other);

    
private:
    //==========================================================================
    // PRIVATE DATA MEMBERS
    //==========================================================================
    QtPcbLayerId m_layer;           // Primary layer for this item
    bool m_isKnockout;              // Whether this item creates a knockout
    bool m_isLocked;                // Whether this item is locked
    QtPcbGroup* m_group;            // Parent group (if any)
};

/**
 * @brief Singleton for deleted board items
 * 
 * Used for weak references that no longer exist.
 */
class QtDeletedBoardItem : public EDA_BOARD_OBJECT_DATA
{
    
public:
    static QtDeletedBoardItem* getInstance();
    
    QString getItemDescription(bool full = false) const ;
    QString getClassName() const override { return "QtDeletedBoardItem"; }
    QString getClass() const override { return "QtDeletedBoardItem"; }
    
    // Pure virtual implementations
    void setPosition(const QPointF& position) override {}
    QPointF getPosition() const override { return QPointF(0, 0); }
    
    double similarity(const EDA_BOARD_OBJECT_DATA& other) const override {
        return (this == &other) ? 1.0 : 0.0;
    }
    
    bool operator==(const EDA_BOARD_OBJECT_DATA& other) const override {
        return (this == &other);
    }
    
private:
    QtDeletedBoardItem() : EDA_BOARD_OBJECT_DATA(nullptr, QtKicadType::NotUsed) {}
    static QtDeletedBoardItem* s_instance;
};

//=============================================================================
// TYPE DECLARATIONS (Qt metatype system removed)
//=============================================================================

// Hash function for QHash support
inline uint qHash(QtPcbLayerId layer, uint seed = 0) {
    return static_cast<uint>(qHash(static_cast<int>(layer), seed));
}

inline uint qHash(const QtLayerSet& layerSet, uint seed = 0) {
    // Create hash based on the sequence of layers
    uint hash = seed;
    QVector<QtPcbLayerId> layers = layerSet.sequence();
    for (QtPcbLayerId layer : layers) {
        hash = static_cast<uint>(qHash(static_cast<int>(layer), hash));
    }
    return hash;
}

//=============================================================================
// HELPER FUNCTIONS
//=============================================================================

// Layer conversion helpers
inline bool isCopperLayer(QtPcbLayerId layer) {
    return (layer >= QtPcbLayerId::FCu && layer <= QtPcbLayerId::In30Cu) ||
           layer == QtPcbLayerId::BCu;
}

inline bool isTechLayer(QtPcbLayerId layer) {
    return (layer >= QtPcbLayerId::FMask && layer <= QtPcbLayerId::BFab) &&
           !isCopperLayer(layer);
}

inline bool isUserLayer(QtPcbLayerId layer) {
    return layer >= QtPcbLayerId::User1 && layer <= QtPcbLayerId::User9;
}

inline bool isSolderMaskLayer(QtPcbLayerId layer) {
    return layer == QtPcbLayerId::FMask || layer == QtPcbLayerId::BMask;
}

